\# Briefing de Mission : E-2-Correction - Remédiation et Migration Playwright



\## 1. OBJECTIF STRATÉGIQUE

Corriger l'ensemble des points critiques et des optimisations soulevés par la revue de code du bot Gemini sur la Pull Request de l'Épique E-2. L'objectif est de produire une branche saine, performante, entièrement testée et de migrer les tests E2E de Cypress vers Playwright MCP.



\## 2. CRITÈRES DE SUCCÈS (DEFINITION OF DONE)

\*L'agent doit s'assurer que tous les points suivants sont validés.\*



\- \[ ] \*\*Tests Unitaires :\*\*

&nbsp;   - \[ ] Le fichier `createEmprunt.test.ts` contient des tests unitaires complets (succès, erreurs de permission, erreurs de validation, cas limites).

&nbsp;   - \[ ] Le fichier `generateEmpruntLabels.test.ts` contient des tests unitaires complets.

&nbsp;   - \[ ] Le fichier `updateEmpruntStatus.test.ts` contient des tests unitaires complets.

&nbsp;   - \[ ] La commande `npm run test:ci` dans le dossier `functions` passe avec 100% de succès.

\- \[ ] \*\*Performance :\*\*

&nbsp;   - \[ ] Le problème de requête N+1 dans `generateEmpruntLabels.ts` est résolu en utilisant une lecture groupée des documents.

&nbsp;   - \[ ] Le problème de requête N+1 dans `updateEmpruntStatus.ts` est résolu.

&nbsp;   - \[ ] Le test de performance dans `performance.test.ts` utilise la vraie logique de génération PDF et non un `setTimeout`.

\- \[ ] \*\*Qualité du code :\*\*

&nbsp;   - \[ ] Tous les `context: any` sont remplacés par le type `functions.https.CallableContext`.

\- \[ ] \*\*Tests E2E :\*\*

&nbsp;   - \[ ] Les anciens fichiers et le dossier `cypress` sont complètement supprimés.

&nbsp;   - \[ ] Un nouveau test E2E est créé en utilisant \*\*Playwright MCP\*\*. Le fichier de test doit être placé dans un nouveau dossier, par exemple `tests/e2e/emprunt-creation.spec.ts`.

&nbsp;   - \[ ] Le test Playwright valide le scénario de création d'emprunt.

&nbsp;   - \[ ] Les `wait` fixes sont remplacés par des attentes sur les requêtes réseau pour fiabiliser le test.

\- \[ ] \*\*Validation Finale :\*\*

&nbsp;   - \[ ] Une nouvelle Pull Request est créée.

&nbsp;   - \[ ] Le workflow `GitHub Actions` est 100% vert sur cette nouvelle PR.



\## 3. CONTEXTE TECHNIQUE SPÉCIFIQUE

\- \*\*Source de vérité pour les corrections\*\* : Le commentaire de revue de code du bot Gemini (fourni ci-après).

\- \*\*Structure de projet à respecter\*\* : Se conformer scrupuleusement à l'arborescence définie dans `@docs/Architecture\_SIGMA\_v1.2.md`. Ne pas créer de nouveaux dossiers à la racine.

\- \*\*Outils prioritaires\*\* :

&nbsp;   - Utiliser \*\*SERENA MCP\*\* pour toutes les modifications de code et l'exécution des tests unitaires.

&nbsp;   - Utiliser \*\*Playwright MCP\*\* pour les tests E2E. Il ne faut plus utiliser Cypress.

&nbsp;   - Utiliser \*\*Firebase MCP\*\* si nécessaire pour valider des données ou des règles de sécurité.



\## 4. SCÉNARIO DE TEST D'ACCEPTATION (UAT avec Playwright)

\*L'agent devra automatiser ce scénario avec Playwright MCP.\*

1\. Naviguer vers la page de création d'emprunt.

2\. Remplir le formulaire avec des données valides.

3\. Valider la soumission.

4\. Intercepter la requête réseau de création et attendre sa réponse pour confirmer le succès.

5\. Vérifier que le message de succès s'affiche sur l'interface.



---

\*\*RÉFÉRENCE - COMMENTAIRE DU BOT GEMINI À CORRIGER :\*\*

\[Collez ici l'intégralité du commentaire de revue de code du bot Gemini que vous avez partagé]

---

