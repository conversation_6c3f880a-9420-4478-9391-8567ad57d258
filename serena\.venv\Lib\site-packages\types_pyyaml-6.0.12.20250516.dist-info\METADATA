Metadata-Version: 2.4
Name: types-PyYAML
Version: 6.0.12.20250516
Summary: Typing stubs for PyYAML
License-Expression: Apache-2.0
Project-URL: Homepage, https://github.com/python/typeshed
Project-URL: GitHub, https://github.com/python/typeshed
Project-URL: Changes, https://github.com/typeshed-internal/stub_uploader/blob/main/data/changelogs/PyYAML.md
Project-URL: Issue tracker, https://github.com/python/typeshed/issues
Project-URL: Chat, https://gitter.im/python/typing
Classifier: Programming Language :: Python :: 3
Classifier: Typing :: Stubs Only
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Dynamic: license-file

## Typing stubs for PyYAML

This is a [PEP 561](https://peps.python.org/pep-0561/)
type stub package for the [`PyYAML`](https://github.com/yaml/pyyaml) package.
It can be used by type-checking tools like
[mypy](https://github.com/python/mypy/),
[pyright](https://github.com/microsoft/pyright),
[pytype](https://github.com/google/pytype/),
[Pyre](https://pyre-check.org/),
PyCharm, etc. to check code that uses `PyYAML`. This version of
`types-PyYAML` aims to provide accurate annotations for
`PyYAML==6.0.*`.

This package is part of the [typeshed project](https://github.com/python/typeshed).
All fixes for types and metadata should be contributed there.
See [the README](https://github.com/python/typeshed/blob/main/README.md)
for more details. The source for this package can be found in the
[`stubs/PyYAML`](https://github.com/python/typeshed/tree/main/stubs/PyYAML)
directory.

This package was tested with
mypy 1.15.0,
pyright 1.1.400,
and pytype 2024.10.11.
It was generated from typeshed commit
[`126768408a69b7a3a09b7d3992970b289f92937e`](https://github.com/python/typeshed/commit/126768408a69b7a3a09b7d3992970b289f92937e).
