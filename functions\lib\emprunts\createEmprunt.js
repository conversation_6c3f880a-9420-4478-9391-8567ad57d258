"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createEmprunt = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const auth_1 = require("../utils/auth");
const validation_1 = require("../utils/validation");
const db = admin.firestore();
/**
 * Cloud Function pour créer un nouvel emprunt
 * Accessible uniquement aux régisseurs et admins
 */
exports.createEmprunt = functions.https.onCall(async (data, context) => {
    try {
        // Vérification des permissions
        (0, auth_1.checkRegisseurOrAdmin)(context);
        // Validation des données
        const validatedData = (0, validation_1.validateEmpruntData)(data);
        // Création de l'emprunt avec transaction
        const result = await db.runTransaction(async (transaction) => {
            // Génération de l'ID de l'emprunt
            const empruntRef = db.collection("emprunts").doc();
            // Vérification de la disponibilité du matériel
            if (validatedData.materiel && validatedData.materiel.length > 0) {
                await validateMaterielAvailability(transaction, validatedData.materiel);
            }
            // Données de l'emprunt principal
            const empruntDoc = {
                nom: validatedData.nom,
                lieu: validatedData.lieu,
                dateDepart: admin.firestore.Timestamp.fromDate(validatedData.dateDepart),
                dateRetourPrevue: admin.firestore.Timestamp.fromDate(validatedData.dateRetourPrevue),
                dateRetourEffective: null,
                secteur: validatedData.secteur,
                referent: validatedData.referent,
                emprunteur: validatedData.emprunteur,
                notes: validatedData.notes || "",
                statut: "Pas prêt",
                estInventorie: false,
                estFacture: false,
                createdBy: context.auth.uid,
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            };
            // Création du document emprunt
            transaction.set(empruntRef, empruntDoc);
            // Ajout du matériel dans la sous-collection
            if (validatedData.materiel && validatedData.materiel.length > 0) {
                for (const item of validatedData.materiel) {
                    const materielRef = empruntRef.collection("materiel").doc();
                    transaction.set(materielRef, {
                        idMateriel: item.idMateriel,
                        type: item.type,
                        quantite: item.quantite,
                        estRetourne: false,
                        estComplet: false,
                        notes: "",
                    });
                    // Mise à jour des stocks si nécessaire
                    if (item.type === "stock") {
                        await updateStockQuantity(transaction, item.idMateriel, -item.quantite, empruntRef.id, context.auth.uid);
                    }
                }
            }
            // Ajout de l'entrée dans l'historique
            const historiqueRef = empruntRef.collection("historique").doc();
            transaction.set(historiqueRef, {
                date: admin.firestore.FieldValue.serverTimestamp(),
                action: "Création de l'emprunt",
                utilisateur: context.auth.uid,
                notes: `Emprunt créé par ${context.auth.token.email || "utilisateur inconnu"}`,
            });
            return {
                id: empruntRef.id,
                ...empruntDoc,
            };
        });
        functions.logger.info(`Emprunt créé avec succès: ${result.id}`, {
            empruntId: result.id,
            createdBy: context.auth.uid,
        });
        return {
            success: true,
            emprunt: result,
        };
    }
    catch (error) {
        functions.logger.error("Erreur lors de la création de l'emprunt:", error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError("internal", "Erreur interne lors de la création de l'emprunt");
    }
});
/**
 * Valide la disponibilité du matériel demandé
 */
async function validateMaterielAvailability(transaction, materiel) {
    for (const item of materiel) {
        if (item.type === "module") {
            // Vérifier que le module existe et est disponible
            const moduleRef = db.collection("modules").doc(item.idMateriel);
            const moduleDoc = await transaction.get(moduleRef);
            if (!moduleDoc.exists) {
                throw new functions.https.HttpsError("not-found", `Module non trouvé: ${item.idMateriel}`);
            }
            const moduleData = moduleDoc.data();
            if (!moduleData.estPret) {
                throw new functions.https.HttpsError("failed-precondition", `Module non disponible: ${moduleData.nom}`);
            }
        }
        else if (item.type === "stock") {
            // Vérifier que le stock est suffisant
            const stockRef = db.collection("stocks").doc(item.idMateriel);
            const stockDoc = await transaction.get(stockRef);
            if (!stockDoc.exists) {
                throw new functions.https.HttpsError("not-found", `Article en stock non trouvé: ${item.idMateriel}`);
            }
            const stockData = stockDoc.data();
            if (stockData.quantite < item.quantite) {
                throw new functions.https.HttpsError("failed-precondition", `Stock insuffisant pour ${stockData.nom}. Disponible: ${stockData.quantite}, Demandé: ${item.quantite}`);
            }
        }
    }
}
/**
 * Met à jour la quantité en stock
 */
async function updateStockQuantity(transaction, stockId, quantityChange, empruntId, userId) {
    const stockRef = db.collection("stocks").doc(stockId);
    const stockDoc = await transaction.get(stockRef);
    if (stockDoc.exists) {
        const currentQuantity = stockDoc.data().quantite;
        transaction.update(stockRef, {
            quantite: currentQuantity + quantityChange,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        // Ajouter un mouvement de stock
        const mouvementRef = stockRef.collection("mouvements").doc();
        transaction.set(mouvementRef, {
            date: admin.firestore.FieldValue.serverTimestamp(),
            type: quantityChange > 0 ? "entree" : "sortie",
            quantite: Math.abs(quantityChange),
            motif: "Emprunt",
            refEmprunt: empruntId || "",
            utilisateur: userId || "system",
        });
    }
}
